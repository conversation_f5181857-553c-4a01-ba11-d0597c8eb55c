# Options Premium Chart GUI

A PyQt6 application that displays net premiums over strike price using pyqtgraph with a dark theme.

## Features

- **Dark Theme**: Modern dark UI for comfortable viewing
- **Interactive Chart**: Real-time plotting of net premiums vs strike prices using pyqtgraph
- **Ticker Selection**: Dropdown to select different stock tickers (AAPL, GOOGL, MSFT, TSLA, AMZN, NVDA, META, SPY)
- **Expiration Date Selection**: Dropdown with weekly expiration dates for the next 8 weeks
- **Multiple Premium Lines**: Shows net premium, call premium, and put premium for comparison
- **Interactive Legend**: Click to show/hide different premium lines

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

Run the application:
```bash
python main.py
```

## Controls

- **Ticker Dropdown**: Select the stock symbol to analyze
- **Expiration Dropdown**: Choose the options expiration date
- **Refresh Data Button**: Update the chart with current selections

## Chart Features

- **Green Line**: Net Premium (Call Premium - Put Premium)
- **Red Dashed Line**: Call Premium
- **<PERSON>an Dashed Line**: Put Premium
- **Interactive**: Zoom, pan, and hover over data points
- **Grid**: Helpful grid lines for reading values

## Sample Data

The application currently uses simulated options data for demonstration purposes. The premium calculations are simplified but provide a realistic representation of how premiums vary with strike prices.

## Future Enhancements

- Integration with real options data APIs
- Additional options strategies visualization
- Historical data analysis
- Export functionality for charts and data
