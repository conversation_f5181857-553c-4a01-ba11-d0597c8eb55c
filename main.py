import sys
import numpy as np
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QComboBox, QLabel, QPushButton, QFrame)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPalette, QColor
import pyqtgraph as pg

class PremiumChartWidget(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Options Premium Chart")
        self.setGeometry(100, 100, 1200, 800)
        
        # Set dark theme
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QComboBox {
                background-color: #3c3c3c;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 5px;
                border-radius: 3px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #ffffff;
                margin-right: 5px;
            }
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton {
                background-color: #4a4a4a;
                color: #ffffff;
                border: 1px solid #666666;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a5a5a;
            }
            QPushButton:pressed {
                background-color: #3a3a3a;
            }
            QFrame {
                background-color: #2b2b2b;
            }
        """)
        
        # Set up the main widget and layout
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        # Create control panel
        self.create_control_panel(layout)
        
        # Create the chart
        self.create_chart(layout)
        
        # Initialize with sample data
        self.update_chart()
    
    def create_control_panel(self, parent_layout):
        # Control panel frame
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(10, 10, 10, 10)
        
        # Ticker selector
        ticker_label = QLabel("Ticker:")
        self.ticker_combo = QComboBox()
        self.ticker_combo.addItems(["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN", "NVDA", "META", "SPY"])
        self.ticker_combo.setCurrentText("AAPL")
        self.ticker_combo.currentTextChanged.connect(self.on_ticker_changed)
        
        # Expiration date selector
        exp_label = QLabel("Expiration:")
        self.expiration_combo = QComboBox()
        self.populate_expiration_dates()
        self.expiration_combo.currentTextChanged.connect(self.on_expiration_changed)
        
        # Refresh button
        refresh_btn = QPushButton("Refresh Data")
        refresh_btn.clicked.connect(self.update_chart)
        
        # Add widgets to control layout
        control_layout.addWidget(ticker_label)
        control_layout.addWidget(self.ticker_combo)
        control_layout.addSpacing(20)
        control_layout.addWidget(exp_label)
        control_layout.addWidget(self.expiration_combo)
        control_layout.addSpacing(20)
        control_layout.addWidget(refresh_btn)
        control_layout.addStretch()
        
        parent_layout.addWidget(control_frame)
    
    def create_chart(self, parent_layout):
        # Set pyqtgraph to use dark theme
        pg.setConfigOption('background', '#2b2b2b')
        pg.setConfigOption('foreground', '#ffffff')
        
        # Create the plot widget
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setLabel('left', 'Net Premium ($)', color='#ffffff', size='12pt')
        self.plot_widget.setLabel('bottom', 'Strike Price ($)', color='#ffffff', size='12pt')
        self.plot_widget.setTitle('Net Premium vs Strike Price', color='#ffffff', size='14pt')
        
        # Customize grid and axes
        self.plot_widget.showGrid(x=True, y=True, alpha=0.3)
        self.plot_widget.getAxis('left').setPen(color='#ffffff', width=1)
        self.plot_widget.getAxis('bottom').setPen(color='#ffffff', width=1)
        self.plot_widget.getAxis('left').setTextPen(color='#ffffff')
        self.plot_widget.getAxis('bottom').setTextPen(color='#ffffff')
        
        parent_layout.addWidget(self.plot_widget)
    
    def populate_expiration_dates(self):
        """Generate expiration dates for the next few months"""
        self.expiration_combo.clear()
        current_date = datetime.now()
        
        # Generate weekly expirations for the next 8 weeks
        for i in range(8):
            # Find next Friday
            days_ahead = 4 - current_date.weekday()  # Friday is 4
            if days_ahead <= 0:  # Target day already happened this week
                days_ahead += 7
            
            exp_date = current_date + timedelta(days=days_ahead + (i * 7))
            date_str = exp_date.strftime("%Y-%m-%d")
            self.expiration_combo.addItem(date_str)
    
    def generate_sample_data(self):
        """Generate sample options data for demonstration"""
        ticker = self.ticker_combo.currentText()
        
        # Simulate current stock price based on ticker
        stock_prices = {
            "AAPL": 175.0, "GOOGL": 140.0, "MSFT": 380.0, "TSLA": 250.0,
            "AMZN": 145.0, "NVDA": 450.0, "META": 320.0, "SPY": 450.0
        }
        current_price = stock_prices.get(ticker, 100.0)
        
        # Generate strike prices around current price
        strikes = np.arange(current_price * 0.8, current_price * 1.2, current_price * 0.01)
        
        # Generate sample premium data (simplified Black-Scholes-like calculation)
        call_premiums = []
        put_premiums = []
        
        for strike in strikes:
            # Simplified premium calculation for demonstration
            moneyness = current_price / strike
            time_value = 0.5  # Simplified time value
            
            # Call premium (decreases as strike increases)
            call_premium = max(0, current_price - strike) + time_value * np.exp(-0.5 * (moneyness - 1)**2)
            call_premiums.append(call_premium)
            
            # Put premium (increases as strike increases)
            put_premium = max(0, strike - current_price) + time_value * np.exp(-0.5 * (moneyness - 1)**2)
            put_premiums.append(put_premium)
        
        # Calculate net premium (call premium - put premium for a collar strategy)
        net_premiums = np.array(call_premiums) - np.array(put_premiums)
        
        return strikes, net_premiums, call_premiums, put_premiums
    
    def update_chart(self):
        """Update the chart with new data"""
        self.plot_widget.clear()
        
        strikes, net_premiums, call_premiums, put_premiums = self.generate_sample_data()
        
        # Plot net premium
        pen_net = pg.mkPen(color='#00ff00', width=3)  # Green for net premium
        self.plot_widget.plot(strikes, net_premiums, pen=pen_net, name='Net Premium')
        
        # Plot call and put premiums for reference
        pen_call = pg.mkPen(color='#ff6b6b', width=2, style=Qt.PenStyle.DashLine)  # Red dashed for calls
        pen_put = pg.mkPen(color='#4ecdc4', width=2, style=Qt.PenStyle.DashLine)   # Cyan dashed for puts
        
        self.plot_widget.plot(strikes, call_premiums, pen=pen_call, name='Call Premium')
        self.plot_widget.plot(strikes, put_premiums, pen=pen_put, name='Put Premium')
        
        # Add legend
        self.plot_widget.addLegend(offset=(10, 10))
        
        # Update title with current selection
        ticker = self.ticker_combo.currentText()
        expiration = self.expiration_combo.currentText()
        title = f'Net Premium vs Strike Price - {ticker} ({expiration})'
        self.plot_widget.setTitle(title, color='#ffffff', size='14pt')
    
    def on_ticker_changed(self):
        """Handle ticker selection change"""
        self.update_chart()
    
    def on_expiration_changed(self):
        """Handle expiration date change"""
        self.update_chart()

def main():
    app = QApplication(sys.argv)
    
    # Set application-wide dark palette
    palette = QPalette()
    palette.setColor(QPalette.ColorRole.Window, QColor(43, 43, 43))
    palette.setColor(QPalette.ColorRole.WindowText, QColor(255, 255, 255))
    palette.setColor(QPalette.ColorRole.Base, QColor(60, 60, 60))
    palette.setColor(QPalette.ColorRole.AlternateBase, QColor(67, 67, 67))
    palette.setColor(QPalette.ColorRole.ToolTipBase, QColor(255, 255, 255))
    palette.setColor(QPalette.ColorRole.ToolTipText, QColor(255, 255, 255))
    palette.setColor(QPalette.ColorRole.Text, QColor(255, 255, 255))
    palette.setColor(QPalette.ColorRole.Button, QColor(74, 74, 74))
    palette.setColor(QPalette.ColorRole.ButtonText, QColor(255, 255, 255))
    palette.setColor(QPalette.ColorRole.BrightText, QColor(255, 0, 0))
    palette.setColor(QPalette.ColorRole.Link, QColor(42, 130, 218))
    palette.setColor(QPalette.ColorRole.Highlight, QColor(42, 130, 218))
    palette.setColor(QPalette.ColorRole.HighlightedText, QColor(0, 0, 0))
    app.setPalette(palette)
    
    window = PremiumChartWidget()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
